using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
using System.Collections.Generic;

namespace HELLSTRIKE
{
    [RequireComponent(typeof(Animator))]
    [RequireComponent(typeof(Rigidbody))]
    [RequireComponent(typeof(Collider))]
    public class DiamondBoss : MonoBehaviour
    {
        [Header("Boss Settings")]
        [SerializeField] private float maxHealth = 400f;
        [SerializeField] private float detectionRange = 20f;
        [SerializeField] private LayerMask playerLayer;
        
        [Header("Movement Settings")]
        [SerializeField] private float floatHeight = 3f;
        [SerializeField] private float floatSpeed = 2f;
        [SerializeField] private float bobAmount = 0.5f;
        [SerializeField] private float bobSpeed = 2f;
        [SerializeField] private float rotationSpeed = 5f;
        [SerializeField] private float chargeSpeed = 8f;
        [SerializeField] private float chargeDamage = 50f;
        [SerializeField] private float chargeRange = 3f;
        
        [Header("Barrage Phase Settings")]
        [SerializeField] private GameObject orbProjectilePrefab;
        [SerializeField] private Transform[] orbSpawnPoints;
        [SerializeField] private float orbSpeed = 5f;
        [SerializeField] private float orbDamage = 20f;
        [SerializeField] private float barrageInterval = 0.3f;
        [SerializeField] private int orbsPerBarrage = 8;
        [SerializeField] private float barrageDuration = 5f;
        
        [Header("Phase Settings")]
        [SerializeField] private float phaseChangeHealthPercent = 0.25f; // 25% health lost per phase
        [SerializeField] private float phaseDuration = 8f;
        
        [Header("Audio Settings")]
        [SerializeField] private AudioClip barrageSound;
        [SerializeField] [Range(0f, 1f)] private float barrageSoundVolume = 0.8f;
        [SerializeField] private AudioClip chargeSound;
        [SerializeField] [Range(0f, 1f)] private float chargeSoundVolume = 0.9f;
        [SerializeField] private AudioClip phaseChangeSound;
        [SerializeField] [Range(0f, 1f)] private float phaseChangeSoundVolume = 1f;
        [SerializeField] private AudioClip deathSound;
        [SerializeField] [Range(0f, 1f)] private float deathSoundVolume = 1f;
        
        // Boss states
        public enum BossPhase
        {
            Inactive,
            BarragePhase,
            AssaultPhase
        }
        
        // Private variables
        private Transform player;
        private Animator animator;
        private Rigidbody rb;
        private AudioSource audioSource;
        private BossHealthBarUI bossHealthUI;
        private BossMusicManager musicManager;
        
        private float currentHealth;
        private bool isDead = false;
        private bool isTriggered = false;
        private BossPhase currentPhase = BossPhase.Inactive;
        private float phaseTimer = 0f;
        private float barrageTimer = 0f;
        private bool isCharging = false;
        private bool isSpinning = false;
        private int currentPhaseNumber = 0;
        private Vector3 originalPosition;
        private float bobTimer = 0f;

        // Coroutines
        private Coroutine phaseCoroutine;
        private Coroutine barrageCoroutine;
        private Coroutine floatCoroutine;
        
        private void Awake()
        {
            animator = GetComponent<Animator>();
            rb = GetComponent<Rigidbody>();
            audioSource = GetComponent<AudioSource>();

            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            // Configure audio source for 3D sound
            audioSource.spatialBlend = 1f;
            audioSource.rolloffMode = AudioRolloffMode.Linear;
            audioSource.maxDistance = 40f;

            // Configure rigidbody for floating
            if (rb != null)
            {
                rb.useGravity = false;
                rb.isKinematic = true; // We'll control movement manually
                rb.freezeRotation = true; // Stop random rotation
            }
        }
        
        private void Start()
        {
            // Initialize health
            currentHealth = maxHealth;

            // Store original position and set floating height immediately
            originalPosition = transform.position;
            transform.position = originalPosition + Vector3.up * floatHeight;

            // Find player
            player = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (player == null)
            {
                Debug.LogError("DiamondBoss: No GameObject with 'Player' tag found!");
                return;
            }
            
            // Find boss health UI
            bossHealthUI = FindFirstObjectByType<BossHealthBarUI>();
            if (bossHealthUI == null)
            {
                Debug.LogWarning("DiamondBoss: No BossHealthBarUI found in scene!");
            }
            
            // Find music manager
            musicManager = FindFirstObjectByType<BossMusicManager>();
            if (musicManager == null)
            {
                Debug.LogWarning("DiamondBoss: No BossMusicManager found in scene!");
            }
            
            // Validate orb spawn points
            if (orbSpawnPoints == null || orbSpawnPoints.Length == 0)
            {
                Debug.LogWarning("DiamondBoss: No orb spawn points assigned!");
            }
            
            Debug.Log($"DiamondBoss: Initialized with {maxHealth} health, detection range: {detectionRange}");

            // Start checking for player
            StartCoroutine(CheckForPlayer());
        }
        
        private IEnumerator CheckForPlayer()
        {
            while (player != null && !isDead)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, player.position);
                
                if (distanceToPlayer <= detectionRange && !isTriggered)
                {
                    TriggerBoss();
                }
                
                yield return new WaitForSeconds(0.2f);
            }
        }
        
        private void TriggerBoss()
        {
            if (isTriggered) return;
            
            isTriggered = true;
            Debug.Log("DiamondBoss: Boss triggered!");
            
            // Show boss health bar
            if (bossHealthUI != null)
            {
                bossHealthUI.ShowBossHealthBar(this);
            }
            
            // Start boss music
            if (musicManager != null)
            {
                musicManager.StartBossMusic();
            }
            
            // Start first phase
            StartPhase(BossPhase.BarragePhase);
        }
        
        private void StartPhase(BossPhase newPhase)
        {
            // Stop current phase
            if (phaseCoroutine != null)
            {
                StopCoroutine(phaseCoroutine);
            }
            if (barrageCoroutine != null)
            {
                StopCoroutine(barrageCoroutine);
            }
            
            currentPhase = newPhase;
            phaseTimer = 0f;
            isCharging = false;
            isSpinning = false;
            
            // Play phase change sound
            if (phaseChangeSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(phaseChangeSound, phaseChangeSoundVolume);
            }
            
            Debug.Log($"DiamondBoss: Starting {newPhase} phase");
            
            switch (newPhase)
            {
                case BossPhase.BarragePhase:
                    phaseCoroutine = StartCoroutine(BarragePhase());
                    break;
                case BossPhase.AssaultPhase:
                    phaseCoroutine = StartCoroutine(AssaultPhase());
                    break;
            }
        }
        
        private IEnumerator BarragePhase()
        {
            Debug.Log("DiamondBoss: Entering Barrage Phase - floating stationary and firing orbs");

            // Set animation
            if (animator != null)
            {
                animator.SetBool("IsMoving", false);
                animator.SetBool("IsBarraging", true);
            }

            // Start barrage coroutine
            barrageCoroutine = StartCoroutine(FireBarrage());

            // Wait for phase duration
            yield return new WaitForSeconds(phaseDuration);

            // Stop barrage
            if (barrageCoroutine != null)
            {
                StopCoroutine(barrageCoroutine);
            }

            // Set animation
            if (animator != null)
            {
                animator.SetBool("IsBarraging", false);
            }

            // Switch to assault phase
            StartPhase(BossPhase.AssaultPhase);
        }



        private IEnumerator AssaultPhase()
        {
            Debug.Log("DiamondBoss: Entering Assault Phase - moving toward player while floating");

            float phaseEndTime = Time.time + phaseDuration;

            while (Time.time < phaseEndTime && !isDead)
            {
                if (player != null)
                {
                    // Calculate target position (player position + float height)
                    Vector3 targetPosition = player.position + Vector3.up * floatHeight;

                    // Move towards player while maintaining float height
                    transform.position = Vector3.MoveTowards(transform.position, targetPosition, chargeSpeed * Time.deltaTime);

                    // Simple rotation toward player (no crazy spinning)
                    Vector3 lookDirection = (player.position - transform.position).normalized;
                    lookDirection.y = 0; // Keep rotation horizontal only
                    if (lookDirection != Vector3.zero)
                    {
                        Quaternion targetRotation = Quaternion.LookRotation(lookDirection);
                        transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
                    }

                    // Check for collision with player (damage range)
                    float distanceToPlayer = Vector3.Distance(transform.position, player.position);
                    if (distanceToPlayer <= chargeRange)
                    {
                        DamagePlayer();
                        yield return new WaitForSeconds(1f); // Brief pause after hitting player
                    }
                }

                yield return null;
            }

            // Switch back to barrage phase
            StartPhase(BossPhase.BarragePhase);
        }

        private IEnumerator FireBarrage()
        {
            while (currentPhase == BossPhase.BarragePhase && !isDead)
            {
                // Play barrage sound
                if (barrageSound != null && audioSource != null)
                {
                    audioSource.PlayOneShot(barrageSound, barrageSoundVolume);
                }

                // Fire orbs from all spawn points
                if (orbSpawnPoints != null && orbProjectilePrefab != null)
                {
                    for (int i = 0; i < orbsPerBarrage && i < orbSpawnPoints.Length; i++)
                    {
                        if (orbSpawnPoints[i] != null)
                        {
                            FireOrb(orbSpawnPoints[i]);
                        }

                        // Small delay between each orb
                        yield return new WaitForSeconds(0.1f);
                    }
                }

                // Wait before next barrage
                yield return new WaitForSeconds(barrageInterval);
            }
        }

        private void FireOrb(Transform spawnPoint)
        {
            if (player == null || orbProjectilePrefab == null) return;

            // Create orb
            GameObject orb = Instantiate(orbProjectilePrefab, spawnPoint.position, spawnPoint.rotation);

            // Get orb rigidbody
            Rigidbody orbRb = orb.GetComponent<Rigidbody>();
            if (orbRb != null)
            {
                // Calculate ACCURATE direction to player
                Vector3 directionToPlayer = (player.position - spawnPoint.position).normalized;

                // Add SMALL random spread for bullet hell effect (much smaller)
                Vector3 spread = new Vector3(
                    Random.Range(-0.1f, 0.1f),
                    Random.Range(-0.05f, 0.05f),
                    Random.Range(-0.1f, 0.1f)
                );

                Vector3 finalDirection = (directionToPlayer + spread).normalized;

                // Apply force
                orbRb.AddForce(finalDirection * orbSpeed, ForceMode.Impulse);

                Debug.Log($"DiamondBoss: Firing orb from {spawnPoint.position} toward player at {player.position}");
            }

            // Set orb damage if it has BossProjectile script
            BossProjectile projectileScript = orb.GetComponent<BossProjectile>();
            if (projectileScript != null)
            {
                projectileScript.SetDamage(orbDamage);
            }
        }



        private void DamagePlayer()
        {
            if (player == null) return;

            // Play charge sound
            if (chargeSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(chargeSound, chargeSoundVolume);
            }

            // Try multiple methods to find the player
            bool playerDamaged = false;

            // Method 1: Use layer mask if set
            if (playerLayer != 0)
            {
                Collider[] hitColliders = Physics.OverlapSphere(transform.position, chargeRange, playerLayer);
                foreach (var hitCollider in hitColliders)
                {
                    PlayerHealth playerHealth = hitCollider.GetComponent<PlayerHealth>();
                    if (playerHealth != null)
                    {
                        Debug.Log($"DiamondBoss: Dealing {chargeDamage} damage to {hitCollider.name} via charge");
                        playerHealth.TakeDamage(chargeDamage);
                        playerDamaged = true;
                    }
                }
            }

            // Method 2: Direct check on player GameObject if layer mask didn't work
            if (!playerDamaged && player != null)
            {
                PlayerHealth playerHealth = player.GetComponent<PlayerHealth>();
                if (playerHealth != null)
                {
                    Debug.Log($"DiamondBoss: Dealing {chargeDamage} damage to player directly");
                    playerHealth.TakeDamage(chargeDamage);
                    playerDamaged = true;
                }
            }

            if (!playerDamaged)
            {
                Debug.LogWarning("DiamondBoss: Player in range but no PlayerHealth component found!");
            }
        }

        /// <summary>
        /// Public method to damage the boss. Call this from weapons/projectiles.
        /// </summary>
        /// <param name="damage">Amount of damage to deal</param>
        public void TakeDamage(float damage)
        {
            if (isDead) return;

            float previousHealth = currentHealth;
            currentHealth -= damage;
            Debug.Log($"DiamondBoss: Took {damage} damage. Health: {currentHealth}/{maxHealth}");

            // Update boss health UI
            if (bossHealthUI != null)
            {
                bossHealthUI.UpdateBossHealth(currentHealth, maxHealth);
            }

            // Trigger damage animation if available
            if (animator != null)
                animator.SetTrigger("TakeDamage");

            // Check for phase changes (every 25% health lost)
            CheckPhaseChange(previousHealth);

            if (currentHealth <= 0)
            {
                Die();
            }
        }

        private void CheckPhaseChange(float previousHealth)
        {
            if (!isTriggered) return;

            // Calculate how many 25% thresholds we've crossed
            float healthPercent = currentHealth / maxHealth;
            float previousHealthPercent = previousHealth / maxHealth;

            // Check if we crossed a 25% threshold
            int newPhaseNumber = Mathf.FloorToInt((1f - healthPercent) / phaseChangeHealthPercent);

            if (newPhaseNumber > currentPhaseNumber)
            {
                currentPhaseNumber = newPhaseNumber;
                Debug.Log($"DiamondBoss: Health threshold crossed! Phase number: {currentPhaseNumber}");

                // Alternate between phases based on phase number
                BossPhase nextPhase = (currentPhaseNumber % 2 == 1) ? BossPhase.AssaultPhase : BossPhase.BarragePhase;

                if (nextPhase != currentPhase)
                {
                    StartPhase(nextPhase);
                }
            }
        }

        private void Die()
        {
            if (isDead) return;

            isDead = true;
            Debug.Log("DiamondBoss: Died!");

            // Stop all phases
            if (phaseCoroutine != null)
            {
                StopCoroutine(phaseCoroutine);
            }
            if (barrageCoroutine != null)
            {
                StopCoroutine(barrageCoroutine);
            }

            // Play death animation
            if (animator != null)
            {
                animator.SetTrigger("Die");
                animator.SetBool("IsMoving", false);
                animator.SetBool("IsBarraging", false);
                animator.SetBool("IsCharging", false);
            }

            // Play death sound
            if (deathSound != null && audioSource != null)
            {
                // Create a separate audio source that won't be destroyed with the boss immediately
                GameObject soundObject = new GameObject("DiamondBossDeathSound");
                soundObject.transform.position = transform.position;
                AudioSource soundSource = soundObject.AddComponent<AudioSource>();
                soundSource.clip = deathSound;
                soundSource.volume = deathSoundVolume;
                soundSource.spatialBlend = 1f; // Make it 3D audio
                soundSource.rolloffMode = AudioRolloffMode.Linear;
                soundSource.maxDistance = 50f;
                soundSource.Play();

                // Destroy the sound object after the clip finishes playing
                Destroy(soundObject, deathSound.length + 0.1f);
            }

            // Update health bar to show 0 health first, then hide after delay
            if (bossHealthUI != null)
            {
                bossHealthUI.UpdateBossHealth(0, maxHealth);
                StartCoroutine(HideHealthBarAfterDelay());
            }

            // Stop boss music
            if (musicManager != null)
            {
                musicManager.StopBossMusic();
            }

            // Start victory sequence (goes to title scene)
            StartCoroutine(VictorySequence());
        }

        private IEnumerator HideHealthBarAfterDelay()
        {
            // Wait 2 seconds to let player see the health bar reach zero
            yield return new WaitForSeconds(2f);

            if (bossHealthUI != null)
            {
                bossHealthUI.HideBossHealthBar();
            }
        }

        private IEnumerator VictorySequence()
        {
            // Disable colliders to prevent further interactions
            Collider[] colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            // Wait for health bar to be hidden (2 seconds)
            yield return new WaitForSeconds(2f);

            // Additional victory delay to let player appreciate the win
            yield return new WaitForSeconds(2f);

            Debug.Log("DiamondBoss: Victory! Loading Scene_Title...");

            // Load the title scene
            try
            {
                SceneManager.LoadScene("Scene_Title");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"DiamondBoss: Failed to load Scene_Title: {e.Message}");
                Debug.LogError("Make sure 'Scene_Title' is added to Build Settings!");

                // Fallback: just destroy the boss if scene loading fails
                StartCoroutine(DestroyAfterDelay());
            }
        }

        private IEnumerator DestroyAfterDelay()
        {
            // Disable colliders to prevent further interactions
            Collider[] colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            yield return new WaitForSeconds(3f);

            // Optionally fade out mesh renderers before destroying
            MeshRenderer[] renderers = GetComponentsInChildren<MeshRenderer>();
            foreach (var renderer in renderers)
            {
                renderer.enabled = false;
            }

            yield return new WaitForSeconds(1f);

            Destroy(gameObject);
        }

        private void OnDrawGizmosSelected()
        {
            // Visualize detection range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRange);

            // Visualize charge range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, chargeRange);
        }

        // Public methods for health system
        public float GetCurrentHealth() => currentHealth;
        public float GetMaxHealth() => maxHealth;
        public float GetHealthPercentage() => currentHealth / maxHealth;
        public bool IsDead() => isDead;
        public string GetBossName() => "Diamond Boss";
    }
}
