using UnityEngine;
using System.Collections;

namespace HELLSTRIKE
{
    public class BossMusicManager : MonoBehaviour
    {
        [Header("Music Settings")]
        [SerializeField] private AudioClip backgroundMusic;
        [SerializeField] private AudioClip bossMusic;
        [SerializeField] [Range(0f, 1f)] private float backgroundMusicVolume = 0.7f;
        [SerializeField] [Range(0f, 1f)] private float bossMusicVolume = 0.9f;
        
        [Header("Transition Settings")]
        [SerializeField] private float fadeOutDuration = 1f;
        [SerializeField] private float fadeInDuration = 1f;
        [SerializeField] private bool crossFade = true;
        
        [Header("Audio Sources")]
        [SerializeField] private AudioSource backgroundAudioSource;
        [SerializeField] private AudioSource bossAudioSource;
        
        // Private variables
        private bool isBossMusicPlaying = false;
        private bool isTransitioning = false;
        private Coroutine musicTransitionCoroutine;
        
        private void Awake()
        {
            // Create audio sources if not assigned
            if (backgroundAudioSource == null)
            {
                GameObject bgAudioObject = new GameObject("BackgroundMusicSource");
                bgAudioObject.transform.SetParent(transform);
                backgroundAudioSource = bgAudioObject.AddComponent<AudioSource>();
            }
            
            if (bossAudioSource == null)
            {
                GameObject bossAudioObject = new GameObject("BossMusicSource");
                bossAudioObject.transform.SetParent(transform);
                bossAudioSource = bossAudioObject.AddComponent<AudioSource>();
            }
            
            // Configure audio sources
            SetupAudioSource(backgroundAudioSource, backgroundMusic, backgroundMusicVolume, true);
            SetupAudioSource(bossAudioSource, bossMusic, bossMusicVolume, true);
        }
        
        private void Start()
        {
            // Start playing background music if available
            if (backgroundMusic != null && backgroundAudioSource != null)
            {
                PlayBackgroundMusic();
            }
        }
        
        private void SetupAudioSource(AudioSource audioSource, AudioClip clip, float volume, bool loop)
        {
            if (audioSource == null) return;
            
            audioSource.clip = clip;
            audioSource.volume = volume;
            audioSource.loop = loop;
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 0f; // 2D sound for music
        }
        
        /// <summary>
        /// Start playing background music
        /// </summary>
        public void PlayBackgroundMusic()
        {
            if (backgroundAudioSource == null || backgroundMusic == null) return;
            
            if (!backgroundAudioSource.isPlaying && !isBossMusicPlaying)
            {
                backgroundAudioSource.Play();
                Debug.Log("BossMusicManager: Started background music");
            }
        }
        
        /// <summary>
        /// Stop background music
        /// </summary>
        public void StopBackgroundMusic()
        {
            if (backgroundAudioSource != null && backgroundAudioSource.isPlaying)
            {
                backgroundAudioSource.Stop();
                Debug.Log("BossMusicManager: Stopped background music");
            }
        }
        
        /// <summary>
        /// Start boss music and stop background music
        /// </summary>
        public void StartBossMusic()
        {
            if (isBossMusicPlaying || isTransitioning) return;
            
            if (bossMusic == null || bossAudioSource == null)
            {
                Debug.LogWarning("BossMusicManager: Boss music or audio source not assigned!");
                return;
            }
            
            Debug.Log("BossMusicManager: Starting boss music");
            
            if (musicTransitionCoroutine != null)
            {
                StopCoroutine(musicTransitionCoroutine);
            }
            
            musicTransitionCoroutine = StartCoroutine(TransitionToBossMusic());
        }
        
        /// <summary>
        /// Stop boss music and resume background music
        /// </summary>
        public void StopBossMusic()
        {
            if (!isBossMusicPlaying || isTransitioning) return;
            
            Debug.Log("BossMusicManager: Stopping boss music");
            
            if (musicTransitionCoroutine != null)
            {
                StopCoroutine(musicTransitionCoroutine);
            }
            
            musicTransitionCoroutine = StartCoroutine(TransitionToBackgroundMusic());
        }
        
        private IEnumerator TransitionToBossMusic()
        {
            isTransitioning = true;
            
            if (crossFade)
            {
                // Start boss music at volume 0
                bossAudioSource.volume = 0f;
                bossAudioSource.Play();
                
                // Cross-fade between background and boss music
                float elapsedTime = 0f;
                float maxDuration = Mathf.Max(fadeOutDuration, fadeInDuration);
                
                float initialBgVolume = backgroundAudioSource.volume;
                
                while (elapsedTime < maxDuration)
                {
                    elapsedTime += Time.deltaTime;
                    
                    // Fade out background music
                    if (elapsedTime < fadeOutDuration && backgroundAudioSource.isPlaying)
                    {
                        float bgProgress = elapsedTime / fadeOutDuration;
                        backgroundAudioSource.volume = Mathf.Lerp(initialBgVolume, 0f, bgProgress);
                    }
                    
                    // Fade in boss music
                    if (elapsedTime < fadeInDuration)
                    {
                        float bossProgress = elapsedTime / fadeInDuration;
                        bossAudioSource.volume = Mathf.Lerp(0f, bossMusicVolume, bossProgress);
                    }
                    
                    yield return null;
                }
                
                // Ensure final volumes
                backgroundAudioSource.volume = 0f;
                bossAudioSource.volume = bossMusicVolume;
                
                // Stop background music
                backgroundAudioSource.Stop();
            }
            else
            {
                // Simple transition: stop background, start boss
                StopBackgroundMusic();
                yield return new WaitForSeconds(0.1f);
                bossAudioSource.Play();
            }
            
            isBossMusicPlaying = true;
            isTransitioning = false;
            
            Debug.Log("BossMusicManager: Transition to boss music complete");
        }
        
        private IEnumerator TransitionToBackgroundMusic()
        {
            isTransitioning = true;
            
            if (crossFade && backgroundMusic != null)
            {
                // Start background music at volume 0
                backgroundAudioSource.volume = 0f;
                backgroundAudioSource.Play();
                
                // Cross-fade between boss and background music
                float elapsedTime = 0f;
                float maxDuration = Mathf.Max(fadeOutDuration, fadeInDuration);
                
                float initialBossVolume = bossAudioSource.volume;
                
                while (elapsedTime < maxDuration)
                {
                    elapsedTime += Time.deltaTime;
                    
                    // Fade out boss music
                    if (elapsedTime < fadeOutDuration && bossAudioSource.isPlaying)
                    {
                        float bossProgress = elapsedTime / fadeOutDuration;
                        bossAudioSource.volume = Mathf.Lerp(initialBossVolume, 0f, bossProgress);
                    }
                    
                    // Fade in background music
                    if (elapsedTime < fadeInDuration && backgroundMusic != null)
                    {
                        float bgProgress = elapsedTime / fadeInDuration;
                        backgroundAudioSource.volume = Mathf.Lerp(0f, backgroundMusicVolume, bgProgress);
                    }
                    
                    yield return null;
                }
                
                // Ensure final volumes
                bossAudioSource.volume = 0f;
                if (backgroundMusic != null)
                {
                    backgroundAudioSource.volume = backgroundMusicVolume;
                }
                
                // Stop boss music
                bossAudioSource.Stop();
            }
            else
            {
                // Simple transition: stop boss, start background
                bossAudioSource.Stop();
                yield return new WaitForSeconds(0.1f);
                if (backgroundMusic != null)
                {
                    PlayBackgroundMusic();
                }
            }
            
            isBossMusicPlaying = false;
            isTransitioning = false;
            
            Debug.Log("BossMusicManager: Transition to background music complete");
        }
        
        // Public getters
        public bool IsBossMusicPlaying() => isBossMusicPlaying;
        public bool IsTransitioning() => isTransitioning;
        
        // Public setters for runtime changes
        public void SetBackgroundMusic(AudioClip newClip)
        {
            backgroundMusic = newClip;
            if (backgroundAudioSource != null)
            {
                backgroundAudioSource.clip = newClip;
            }
        }
        
        public void SetBossMusic(AudioClip newClip)
        {
            bossMusic = newClip;
            if (bossAudioSource != null)
            {
                bossAudioSource.clip = newClip;
            }
        }
        
        public void SetBackgroundVolume(float volume)
        {
            backgroundMusicVolume = Mathf.Clamp01(volume);
            if (backgroundAudioSource != null && !isBossMusicPlaying)
            {
                backgroundAudioSource.volume = backgroundMusicVolume;
            }
        }
        
        public void SetBossVolume(float volume)
        {
            bossMusicVolume = Mathf.Clamp01(volume);
            if (bossAudioSource != null && isBossMusicPlaying)
            {
                bossAudioSource.volume = bossMusicVolume;
            }
        }
    }
}
