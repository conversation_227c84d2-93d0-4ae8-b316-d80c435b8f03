using UnityEngine;
using UnityEngine.UI;

namespace HELLSTRIKE
{
    /// <summary>
    /// Automatic setup script for creating boss health bar UI.
    /// Add this to any GameObject temporarily, run the setup, then remove this script.
    /// </summary>
    public class BossHealthBarSetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [SerializeField] private bool createBossHealthBarOnStart = false;
        
        [Header("Health Bar Settings")]
        [SerializeField] private Vector2 healthBarPosition = new Vector2(0, -50);
        [SerializeField] private Vector2 healthBarSize = new Vector2(400, 60);
        [SerializeField] private Color backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
        [SerializeField] private Color healthBarColor = Color.red;
        
        void Start()
        {
            if (createBossHealthBarOnStart)
            {
                CreateBossHealthBarUI();
            }
        }
        
        [ContextMenu("Create Boss Health Bar UI")]
        public void CreateBossHealthBarUI()
        {
            // Find or create Canvas
            Canvas canvas = FindFirstObjectByType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasGO = new GameObject("Canvas");
                canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                
                // Add Canvas Scaler
                CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                
                // Add GraphicRaycaster
                canvasGO.AddComponent<GraphicRaycaster>();
                
                Debug.Log("BossHealthBarSetup: Created Canvas");
            }
            
            // Create Boss Health Bar Panel
            GameObject panelGO = new GameObject("BossHealthBarPanel");
            panelGO.transform.SetParent(canvas.transform, false);
            
            // Add Image component to panel (optional background)
            Image panelImage = panelGO.AddComponent<Image>();
            panelImage.color = new Color(0, 0, 0, 0.3f); // Semi-transparent background
            
            // Setup panel RectTransform
            RectTransform panelRect = panelGO.GetComponent<RectTransform>();
            panelRect.anchorMin = new Vector2(0.5f, 1f); // Top-center
            panelRect.anchorMax = new Vector2(0.5f, 1f);
            panelRect.anchoredPosition = healthBarPosition;
            panelRect.sizeDelta = healthBarSize;
            
            // Create Health Bar Background
            GameObject backgroundGO = new GameObject("HealthBarBackground");
            backgroundGO.transform.SetParent(panelGO.transform, false);
            
            Image backgroundImage = backgroundGO.AddComponent<Image>();
            backgroundImage.color = backgroundColor;
            
            RectTransform backgroundRect = backgroundGO.GetComponent<RectTransform>();
            backgroundRect.anchorMin = Vector2.zero;
            backgroundRect.anchorMax = Vector2.one;
            backgroundRect.offsetMin = new Vector2(10, 10); // Left, Bottom margins
            backgroundRect.offsetMax = new Vector2(-10, -30); // Right, Top margins
            
            // Create Red Health Bar Fill
            GameObject fillGO = new GameObject("HealthBarFill");
            fillGO.transform.SetParent(backgroundGO.transform, false);
            
            Image fillImage = fillGO.AddComponent<Image>();
            fillImage.color = healthBarColor;
            fillImage.type = Image.Type.Filled;
            fillImage.fillMethod = Image.FillMethod.Horizontal;
            
            RectTransform fillRect = fillGO.GetComponent<RectTransform>();
            fillRect.anchorMin = Vector2.zero;
            fillRect.anchorMax = Vector2.one;
            fillRect.offsetMin = Vector2.zero;
            fillRect.offsetMax = Vector2.zero;
            
            // Create Boss Name Text
            GameObject nameTextGO = new GameObject("BossNameText");
            nameTextGO.transform.SetParent(panelGO.transform, false);

            Text nameText = nameTextGO.AddComponent<Text>();
            nameText.text = "Diamond Boss";
            nameText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            nameText.fontSize = 16;
            nameText.color = Color.white;
            nameText.alignment = TextAnchor.MiddleCenter;
            
            RectTransform nameTextRect = nameTextGO.GetComponent<RectTransform>();
            nameTextRect.anchorMin = new Vector2(0, 0.5f);
            nameTextRect.anchorMax = new Vector2(1, 1);
            nameTextRect.offsetMin = new Vector2(10, 0);
            nameTextRect.offsetMax = new Vector2(-10, -5);
            
            // Create Health Text
            GameObject healthTextGO = new GameObject("BossHealthText");
            healthTextGO.transform.SetParent(panelGO.transform, false);

            Text healthText = healthTextGO.AddComponent<Text>();
            healthText.text = "400/400";
            healthText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            healthText.fontSize = 14;
            healthText.color = Color.white;
            healthText.alignment = TextAnchor.MiddleCenter;
            
            RectTransform healthTextRect = healthTextGO.GetComponent<RectTransform>();
            healthTextRect.anchorMin = new Vector2(0, 0);
            healthTextRect.anchorMax = new Vector2(1, 0.5f);
            healthTextRect.offsetMin = new Vector2(10, 5);
            healthTextRect.offsetMax = new Vector2(-10, 0);
            
            // Add BossHealthBarUI script
            BossHealthBarUI healthBarUI = panelGO.AddComponent<BossHealthBarUI>();
            
            // Auto-assign components using reflection to access private fields
            var panelField = typeof(BossHealthBarUI).GetField("bossHealthBarPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var fillField = typeof(BossHealthBarUI).GetField("bossHealthBarFill",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var backgroundField = typeof(BossHealthBarUI).GetField("bossHealthBarBackground",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var nameField = typeof(BossHealthBarUI).GetField("bossNameText",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var healthField = typeof(BossHealthBarUI).GetField("bossHealthText",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (panelField != null) panelField.SetValue(healthBarUI, panelGO);
            if (fillField != null) fillField.SetValue(healthBarUI, fillImage);
            if (backgroundField != null) backgroundField.SetValue(healthBarUI, backgroundImage);
            if (nameField != null) nameField.SetValue(healthBarUI, nameText);
            if (healthField != null) healthField.SetValue(healthBarUI, healthText);
            
            // Hide the panel initially (boss will show it when triggered)
            panelGO.SetActive(false);

            // Also hide individual components to ensure they're not visible
            backgroundGO.SetActive(false);
            fillGO.SetActive(false);
            nameTextGO.SetActive(false);
            healthTextGO.SetActive(false);

            Debug.Log("✅ Boss Health Bar UI created successfully!");
            Debug.Log("📋 Setup complete:");
            Debug.Log("   - Panel: BossHealthBarPanel (hidden initially)");
            Debug.Log("   - Background: Dark gray (hidden initially)");
            Debug.Log("   - Fill: Red health bar (hidden initially)");
            Debug.Log("   - Text: Boss name and health (hidden initially)");
            Debug.Log("   - Script: BossHealthBarUI attached and configured");
            Debug.Log("   - Animation: Disabled for smooth health bar updates");
            Debug.Log("🎯 You can now remove this BossHealthBarSetup script!");
            
            // Select the created object for easy access
            #if UNITY_EDITOR
            UnityEditor.Selection.activeGameObject = panelGO;
            #endif
        }
        
        [ContextMenu("Find Existing Boss Health Bar")]
        public void FindExistingBossHealthBar()
        {
            BossHealthBarUI existingUI = FindFirstObjectByType<BossHealthBarUI>();
            if (existingUI != null)
            {
                Debug.Log($"✅ Found existing BossHealthBarUI on: {existingUI.gameObject.name}");
                #if UNITY_EDITOR
                UnityEditor.Selection.activeGameObject = existingUI.gameObject;
                #endif
            }
            else
            {
                Debug.Log("❌ No BossHealthBarUI found in scene. Use 'Create Boss Health Bar UI' to create one.");
            }
        }
    }
}
