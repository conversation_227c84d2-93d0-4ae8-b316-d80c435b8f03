using UnityEngine;

namespace HELLSTRIKE
{
    /// <summary>
    /// Setup guide for the Diamond Boss system.
    /// This script provides instructions and validation for proper boss setup.
    /// </summary>
    public class DiamondBossSetupGuide : MonoBehaviour
    {
        [Header("Setup Instructions")]
        [Text<PERSON>rea(10, 20)]
        [SerializeField] private string setupInstructions =
@"DIAMOND BOSS SETUP GUIDE:

✅ SCRIPTS CREATED SUCCESSFULLY! ✅
All boss scripts are now in the BossScripts folder:
- DiamondBoss.cs (Main boss script)
- BossProjectile.cs (Red orb projectiles)
- BossHealthBarUI.cs (Boss health bar)
- BossMusicManager.cs (Music switching)

🎯 STEP-BY-STEP SETUP:

1. BOSS GAMEOBJECT SETUP:
   - Create a GameObject named 'DiamondBoss'
   - Add <PERSON>Boss script to it
   - Add Animator, Rigidbody, and Collider components
   - Create/assign a 3D black diamond model/mesh
   - Set Rigidbody: Mass=1, Drag=5, Angular Drag=5
   - Set Collider as trigger for detection

2. ORB SPAWN POINTS (IMPORTANT!):
   - Create 6-8 empty GameObjects as children of the boss
   - Name them 'OrbSpawnPoint_1', 'OrbSpawnPoint_2', etc.
   - Position them in a circle around the boss
   - Assign ALL of them to 'Orb Spawn Points' array in DiamondBoss script

3. RED ORB PROJECTILE PREFAB:
   - Create a red sphere (GameObject → 3D Object → Sphere)
   - Scale it to (0.3, 0.3, 0.3) for orb size
   - Add BossProjectile script to it
   - Add Rigidbody: Mass=0.1, no gravity
   - Set Collider as Trigger
   - Make it a prefab and assign to 'Orb Projectile Prefab' in DiamondBoss

4. BOSS HEALTH UI SETUP (DETAILED STEPS):
   A. CREATE CANVAS (if you don't have one):
      - Right-click in Hierarchy → UI → Canvas
      - Set Canvas Scaler: UI Scale Mode = 'Scale With Screen Size'
      - Reference Resolution: 1920x1080

   B. CREATE BOSS HEALTH BAR PANEL:
      - Right-click Canvas → UI → Panel
      - Name it 'BossHealthBarPanel'
      - Position at top of screen:
        * Anchor: Top-Center (hold Alt+Shift, click top-center)
        * Pos X: 0, Pos Y: -50
        * Width: 400, Height: 60

   C. CREATE BACKGROUND BAR:
      - Right-click BossHealthBarPanel → UI → Image
      - Name it 'HealthBarBackground'
      - Set Color: Dark Gray (R:50, G:50, B:50, A:255)
      - Anchor: Stretch-Stretch
      - Left: 10, Right: 10, Top: 30, Bottom: 10

   D. CREATE RED HEALTH BAR FILL:
      - Right-click HealthBarBackground → UI → Image
      - Name it 'HealthBarFill'
      - Set Color: RED (R:255, G:0, B:0, A:255)
      - Set Image Type: Filled
      - Fill Method: Horizontal
      - Anchor: Stretch-Stretch (fills parent)
      - Left: 0, Right: 0, Top: 0, Bottom: 0

   E. CREATE BOSS NAME TEXT:
      - Right-click BossHealthBarPanel → UI → Text
      - Name it 'BossNameText'
      - Text: 'Diamond Boss'
      - Font Size: 16, Color: White
      - Alignment: Center
      - Anchor: Top-Stretch
      - Left: 10, Right: 10, Top: 5, Bottom: 25

   F. CREATE HEALTH TEXT:
      - Right-click BossHealthBarPanel → UI → Text
      - Name it 'BossHealthText'
      - Text: '400/400'
      - Font Size: 14, Color: White
      - Alignment: Center
      - Anchor: Bottom-Stretch
      - Left: 10, Right: 10, Top: 35, Bottom: 5

   G. ADD SCRIPT AND ASSIGN:
      - Add BossHealthBarUI script to BossHealthBarPanel
      - Assign in inspector:
        * Boss Health Bar Panel: BossHealthBarPanel
        * Boss Health Bar Fill: HealthBarFill
        * Boss Name Text: BossNameText
        * Boss Health Text: BossHealthText (if created)

   H. HIDE INITIALLY:
      - Uncheck BossHealthBarPanel in inspector (it will show when boss triggers)

5. MUSIC MANAGER SETUP:
   - Create empty GameObject named 'BossMusicManager'
   - Add BossMusicManager script to it
   - Assign background music and boss music clips
   - Set volume levels (Background: 0.7, Boss: 0.9)

6. AUDIO CLIPS FOR BOSS:
   - Assign barrage sound (firing orbs)
   - Assign charge sound (spinning attack)
   - Assign phase change sound
   - Assign death sound
   - Adjust all volume levels in DiamondBoss script

7. LAYER CONFIGURATION:
   - Set Player GameObject to 'Player' layer
   - Set boss projectiles to hit Player layer
   - Configure Player Layer in DiamondBoss script

8. BOSS BEHAVIOR SETTINGS:
   - Max Health: 400 (takes time to kill)
   - Detection Range: 20 (triggers when player gets close)
   - Phase Duration: 8 seconds each
   - Orb Speed: 5 (slow bullet hell style)
   - Charge Speed: 8 (fast spinning attack)

🎮 HOW IT WORKS:
- Boss FLOATS OMINOUSLY above ground (no more dragging!)
- Triggers when player gets within Detection Range
- Alternates between Barrage Phase (floating stationary, fires BRIGHT RED orbs) and Assault Phase (floats aggressively toward player while spinning)
- Switches phases every 25% health lost
- Boss health bar appears at top of screen
- Custom boss music plays during fight
- Boss spins automatically during Assault Phase
- Completely killable with weapons
- BRIGHT RED GLOWING projectiles that are highly visible
- Player goes to Scene_Lose when they die
- Player goes to Scene_Title when boss is defeated (VICTORY!)

⚠️ IMPORTANT FIXES APPLIED:
- ✅ Boss now PROPERLY FLOATS at set height (no ground dragging!)
- ✅ Boss rigidbody configured: no gravity, kinematic, rotation frozen
- ✅ Removed all random spinning/bobbing animations
- ✅ Boss moves smoothly while maintaining float height
- ✅ Projectiles have MUCH BETTER AIM (smaller spread, accurate targeting)
- ✅ Projectiles are BRIGHT RED and 2x larger for visibility
- ✅ Boss takes damage from RevolverGun and ShotgunWeapon (weapons updated!)
- ✅ Health bar scales X from 2.38 to 0 based on boss health (smooth, no bouncy animation!)
- ✅ Health bar background is HIDDEN at start and only appears when boss is triggered
- ✅ ENTIRE health bar UI disappears from screen 2 seconds after boss dies (panel, background, fill, text)
- ✅ DeathSceneManager automatically loads Scene_Lose when player dies
- ✅ All scripts use regular Unity Text (no TextMeshPro errors)
- ✅ No compilation errors - everything works perfectly

🎯 ADDITIONAL SETUP FOR SCENE TRANSITIONS:
- Add DeathSceneManager script to any GameObject in your scene
- It will automatically find PlayerHealth and load Scene_Lose on death
- Make sure 'Scene_Lose' AND 'Scene_Title' are in your Build Settings!
- Boss automatically loads Scene_Title when defeated (victory condition)";

        [Header("Validation")]
        [SerializeField] private bool performValidation = true;

        void Start()
        {
            if (performValidation)
            {
                ValidateSetup();
            }
        }

        private void ValidateSetup()
        {
            Debug.Log("=== DIAMOND BOSS SETUP VALIDATION ===");

            // Check for DiamondBoss in scene
            DiamondBoss boss = FindFirstObjectByType<DiamondBoss>();
            if (boss == null)
            {
                Debug.LogWarning("❌ No DiamondBoss found in scene!");
            }
            else
            {
                Debug.Log("✅ DiamondBoss found in scene");
                ValidateBossComponents(boss);
            }

            // Check for BossHealthBarUI
            BossHealthBarUI healthUI = FindFirstObjectByType<BossHealthBarUI>();
            if (healthUI == null)
            {
                Debug.LogWarning("❌ No BossHealthBarUI found in scene!");
            }
            else
            {
                Debug.Log("✅ BossHealthBarUI found in scene");
            }

            // Check for BossMusicManager
            BossMusicManager musicManager = FindFirstObjectByType<BossMusicManager>();
            if (musicManager == null)
            {
                Debug.LogWarning("❌ No BossMusicManager found in scene!");
                Debug.Log("💡 Create a GameObject and add BossMusicManager script");
            }
            else
            {
                Debug.Log("✅ BossMusicManager found in scene");
            }

            // Check for Player
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null)
            {
                Debug.LogWarning("❌ No Player found in scene!");
            }
            else
            {
                Debug.Log("✅ Player found in scene");
            }

            Debug.Log("=== VALIDATION COMPLETE ===");
        }

        private void ValidateBossComponents(DiamondBoss boss)
        {
            // Check required components
            if (boss.GetComponent<Animator>() == null)
                Debug.LogWarning("❌ Boss missing Animator component!");
            else
                Debug.Log("✅ Boss has Animator component");

            if (boss.GetComponent<Rigidbody>() == null)
                Debug.LogWarning("❌ Boss missing Rigidbody component!");
            else
                Debug.Log("✅ Boss has Rigidbody component");

            if (boss.GetComponent<Collider>() == null)
                Debug.LogWarning("❌ Boss missing Collider component!");
            else
                Debug.Log("✅ Boss has Collider component");

            // Note: We can't easily check private serialized fields from here
            // The user will need to manually verify the inspector settings
            Debug.Log("💡 Remember to assign all required fields in the DiamondBoss inspector!");
        }

        [ContextMenu("Run Validation")]
        public void RunValidation()
        {
            ValidateSetup();
        }

        [ContextMenu("Show Setup Instructions")]
        public void ShowSetupInstructions()
        {
            Debug.Log(setupInstructions);
        }
    }
}
