using UnityEngine;

namespace HELLSTRIKE
{
    [RequireComponent(typeof(Rigidbody))]
    [RequireComponent(typeof(Collider))]
    public class BossProjectile : MonoBehaviour
    {
        [Header("Projectile Settings")]
        [Tooltip("Damage dealt to player on hit")]
        [SerializeField] private float damage = 20f;
        [Tooltip("How long projectile exists before auto-destroying")]
        [SerializeField] private float lifetime = 8f;
        [Tooltip("Layer mask for what the projectile can hit")]
        [SerializeField] private LayerMask hitLayers = -1;

        [Head<PERSON>("Visual Settings")]
        [Tooltip("Scale multiplier for projectile size")]
        [SerializeField] private float projectileScale = 2f;
        [Tooltip("Glow intensity for visibility")]
        [SerializeField] private float glowIntensity = 2f;

        [Header("Visual Effects")]
        [Tooltip("Particle effect to spawn on impact")]
        [SerializeField] private GameObject impactEffect;
        [<PERSON>lt<PERSON>("How long impact effect lasts")]
        [SerializeField] private float impactEffectLifetime = 2f;

        [Header("Audio")]
        [Tooltip("Sound to play on impact")]
        [SerializeField] private AudioClip impactSound;
        [Tooltip("Volume of impact sound")]
        [Range(0f, 1f)]
        [SerializeField] private float impactSoundVolume = 0.8f;

        private Rigidbody rb;
        private bool hasHit = false;

        void Start()
        {
            rb = GetComponent<Rigidbody>();

            // Make projectile more visible
            SetupVisuals();

            // Auto-destroy after lifetime
            Destroy(gameObject, lifetime);

            Debug.Log($"BossProjectile: Created with {damage} damage, {lifetime}s lifetime");
        }

        private void SetupVisuals()
        {
            // Scale up the projectile for better visibility
            transform.localScale = Vector3.one * projectileScale;

            // Make it glow/bright red for visibility
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                // Create a bright red material
                Material glowMaterial = new Material(Shader.Find("Standard"));
                glowMaterial.color = Color.red;
                glowMaterial.SetFloat("_Metallic", 0f);
                glowMaterial.SetFloat("_Glossiness", 0.8f);
                glowMaterial.EnableKeyword("_EMISSION");
                glowMaterial.SetColor("_EmissionColor", Color.red * glowIntensity);
                renderer.material = glowMaterial;
            }
        }

        void OnTriggerEnter(Collider other)
        {
            if (hasHit) return;

            Debug.Log($"BossProjectile: Trigger hit {other.gameObject.name} on layer {other.gameObject.layer}");

            // Check if we hit the player by tag first (most reliable)
            if (other.CompareTag("Player"))
            {
                hasHit = true;
                Debug.Log($"BossProjectile: Hit player by tag!");

                PlayerHealth playerHealth = other.GetComponent<PlayerHealth>();
                if (playerHealth != null)
                {
                    playerHealth.TakeDamage(damage);
                    Debug.Log($"BossProjectile: Dealt {damage} damage to player via tag check");
                }
                else
                {
                    Debug.LogWarning("BossProjectile: Player found but no PlayerHealth component!");
                }

                // Spawn impact effects
                SpawnImpactEffect();
                PlayImpactSound();

                // Destroy the projectile
                Destroy(gameObject);
                return;
            }

            // Fallback: Check if we hit something on the hit layers
            if (((1 << other.gameObject.layer) & hitLayers) != 0)
            {
                hasHit = true;
                Debug.Log($"BossProjectile: Hit {other.gameObject.name} via layer mask");

                // Check if we hit the player
                PlayerHealth playerHealth = other.GetComponent<PlayerHealth>();
                if (playerHealth != null)
                {
                    playerHealth.TakeDamage(damage);
                    Debug.Log($"BossProjectile: Dealt {damage} damage to player via layer check");
                }

                // Spawn impact effects
                SpawnImpactEffect();
                PlayImpactSound();

                // Destroy the projectile
                Destroy(gameObject);
            }
        }

        void OnCollisionEnter(Collision collision)
        {
            if (hasHit) return;

            // Check if we hit something on the hit layers
            if (((1 << collision.gameObject.layer) & hitLayers) == 0) return;

            hasHit = true;

            Debug.Log($"BossProjectile: Collided with {collision.gameObject.name}");

            // Check if we hit the player
            PlayerHealth playerHealth = collision.gameObject.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damage);
                Debug.Log($"BossProjectile: Dealt {damage} damage to player");
            }

            // Spawn impact effects at collision point
            if (collision.contacts.Length > 0)
            {
                SpawnImpactEffect(collision.contacts[0].point, collision.contacts[0].normal);
            }
            else
            {
                SpawnImpactEffect();
            }

            PlayImpactSound();

            // Destroy the projectile
            Destroy(gameObject);
        }

        private void SpawnImpactEffect(Vector3? position = null, Vector3? normal = null)
        {
            if (impactEffect == null) return;

            Vector3 spawnPosition = position ?? transform.position;
            Quaternion spawnRotation = Quaternion.identity;

            if (normal.HasValue)
            {
                spawnRotation = Quaternion.LookRotation(normal.Value);
            }

            GameObject effect = Instantiate(impactEffect, spawnPosition, spawnRotation);
            
            // Auto-destroy the effect after specified lifetime
            Destroy(effect, impactEffectLifetime);

            Debug.Log($"BossProjectile: Spawned impact effect at {spawnPosition}");
        }

        private void PlayImpactSound()
        {
            if (impactSound == null) return;

            // Create a temporary audio source for the impact sound
            GameObject audioObject = new GameObject("BossProjectileImpactSound");
            audioObject.transform.position = transform.position;
            
            AudioSource audioSource = audioObject.AddComponent<AudioSource>();
            audioSource.clip = impactSound;
            audioSource.volume = impactSoundVolume;
            audioSource.spatialBlend = 1f; // 3D sound
            audioSource.rolloffMode = AudioRolloffMode.Linear;
            audioSource.maxDistance = 30f;
            audioSource.Play();

            // Destroy the audio object after the clip finishes
            Destroy(audioObject, impactSound.length + 0.1f);

            Debug.Log($"BossProjectile: Playing impact sound: {impactSound.name}");
        }

        /// <summary>
        /// Set the damage this projectile deals
        /// </summary>
        /// <param name="newDamage">Damage amount</param>
        public void SetDamage(float newDamage)
        {
            damage = newDamage;
        }

        /// <summary>
        /// Set the lifetime of this projectile
        /// </summary>
        /// <param name="newLifetime">Lifetime in seconds</param>
        public void SetLifetime(float newLifetime)
        {
            lifetime = newLifetime;
            
            // Cancel existing destruction and set new one
            CancelInvoke();
            Destroy(gameObject, lifetime);
        }

        /// <summary>
        /// Set the hit layers for this projectile
        /// </summary>
        /// <param name="newHitLayers">Layer mask for what can be hit</param>
        public void SetHitLayers(LayerMask newHitLayers)
        {
            hitLayers = newHitLayers;
        }
    }
}
