using UnityEngine;
using UnityEngine.UI;
using System.Collections;

namespace HELLSTRIKE
{
    public class BossHealthBarUI : MonoBehaviour
    {
        [Header("Boss Health Bar Settings")]
        [SerializeField] private GameObject bossHealthBarPanel;
        [SerializeField] private Image bossHealthBarFill;
        [SerializeField] private Image bossHealthBarBackground;
        [SerializeField] private Text bossNameText;
        [SerializeField] private Text bossHealthText;
        
        [Header("Animation Settings")]
        [SerializeField] private bool enableHealthAnimation = false;
        [SerializeField] private float animationDuration = 0.1f;
        [SerializeField] private AnimationCurve animationCurve = AnimationCurve.Linear(0, 0, 1, 1);
        
        [Header("Colors")]
        [SerializeField] private Color healthBarColor = Color.red;
        [SerializeField] private Color lowHealthColor = Color.yellow;
        [SerializeField] private float lowHealthThreshold = 0.25f;
        
        // Private variables
        private DiamondBoss currentBoss;
        private float targetHealthPercentage = 1f;
        private Coroutine animationCoroutine;
        private bool isVisible = false;
        
        private void Awake()
        {
            // Auto-find components if not assigned
            if (bossHealthBarPanel == null)
            {
                bossHealthBarPanel = gameObject;
            }
            
            if (bossHealthBarFill == null)
            {
                bossHealthBarFill = GetComponentInChildren<Image>();
            }
            
            if (bossNameText == null)
            {
                bossNameText = GetComponentInChildren<Text>();
            }
        }
        
        private void Start()
        {
            // Hide boss health bar initially (including background)
            HideBossHealthBar();
        }
        
        /// <summary>
        /// Show the boss health bar for the specified boss
        /// </summary>
        /// <param name="boss">The boss to track</param>
        public void ShowBossHealthBar(DiamondBoss boss)
        {
            if (boss == null) return;

            currentBoss = boss;
            isVisible = true;

            // Show the main panel
            if (bossHealthBarPanel != null)
            {
                bossHealthBarPanel.SetActive(true);
            }

            // Ensure all components are visible
            if (bossHealthBarBackground != null)
            {
                bossHealthBarBackground.gameObject.SetActive(true);
            }

            if (bossHealthBarFill != null)
            {
                bossHealthBarFill.gameObject.SetActive(true);
            }

            if (bossNameText != null)
            {
                bossNameText.gameObject.SetActive(true);
                bossNameText.text = boss.GetBossName();
            }

            if (bossHealthText != null)
            {
                bossHealthText.gameObject.SetActive(true);
            }

            // Initialize health bar
            UpdateBossHealth(boss.GetCurrentHealth(), boss.GetMaxHealth());

            Debug.Log($"BossHealthBarUI: Showing complete health bar (panel, background, fill, text) for {boss.GetBossName()}");
        }
        
        /// <summary>
        /// Hide the boss health bar completely (panel, background, fill, text)
        /// </summary>
        public void HideBossHealthBar()
        {
            isVisible = false;
            currentBoss = null;

            // Hide the entire panel (this hides everything inside it)
            if (bossHealthBarPanel != null)
            {
                bossHealthBarPanel.SetActive(false);
            }

            // Also explicitly hide individual components as backup
            if (bossHealthBarBackground != null)
            {
                bossHealthBarBackground.gameObject.SetActive(false);
            }

            if (bossHealthBarFill != null)
            {
                bossHealthBarFill.gameObject.SetActive(false);
            }

            if (bossNameText != null)
            {
                bossNameText.gameObject.SetActive(false);
            }

            if (bossHealthText != null)
            {
                bossHealthText.gameObject.SetActive(false);
            }

            Debug.Log("BossHealthBarUI: Complete health bar (panel, background, fill, text) hidden and removed from screen");
        }
        
        /// <summary>
        /// Update the boss health display
        /// </summary>
        /// <param name="currentHealth">Current health value</param>
        /// <param name="maxHealth">Maximum health value</param>
        public void UpdateBossHealth(float currentHealth, float maxHealth)
        {
            if (!isVisible || maxHealth <= 0) return;

            float healthPercentage = Mathf.Clamp01(currentHealth / maxHealth);
            targetHealthPercentage = healthPercentage;

            // Update health text
            if (bossHealthText != null)
            {
                bossHealthText.text = $"{Mathf.RoundToInt(currentHealth)}/{Mathf.RoundToInt(maxHealth)}";
            }

            // Update health bar color based on health percentage
            UpdateHealthBarColor(healthPercentage);

            // Always use instant update (no bouncy animation)
            UpdateHealthBarFill(targetHealthPercentage);
        }
        
        private void UpdateHealthBarColor(float healthPercentage)
        {
            if (bossHealthBarFill == null) return;
            
            Color targetColor = healthPercentage <= lowHealthThreshold ? lowHealthColor : healthBarColor;
            bossHealthBarFill.color = targetColor;
        }
        
        private void UpdateHealthBarFill(float healthPercentage)
        {
            if (bossHealthBarFill != null)
            {
                // Scale X from 2.38 (full health) to 0 (dead)
                float targetScale = Mathf.Lerp(0f, 2.38f, healthPercentage);
                Vector3 newScale = bossHealthBarFill.transform.localScale;
                newScale.x = targetScale;
                bossHealthBarFill.transform.localScale = newScale;
            }
        }
        
        private IEnumerator AnimateHealthChange()
        {
            if (bossHealthBarFill == null) yield break;
            
            float startFillAmount = bossHealthBarFill.fillAmount;
            float elapsedTime = 0f;
            
            while (elapsedTime < animationDuration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / animationDuration;
                float curveValue = animationCurve.Evaluate(progress);
                
                float currentFillAmount = Mathf.Lerp(startFillAmount, targetHealthPercentage, curveValue);
                UpdateHealthBarFill(currentFillAmount);
                
                yield return null;
            }
            
            // Ensure we end at the exact target value
            UpdateHealthBarFill(targetHealthPercentage);
        }
        
        /// <summary>
        /// Force update the display with current boss health
        /// </summary>
        public void ForceUpdateDisplay()
        {
            if (currentBoss != null && isVisible)
            {
                UpdateBossHealth(currentBoss.GetCurrentHealth(), currentBoss.GetMaxHealth());
            }
        }
        
        /// <summary>
        /// Check if the boss health bar is currently visible
        /// </summary>
        /// <returns>True if visible, false otherwise</returns>
        public bool IsVisible()
        {
            return isVisible;
        }
        
        /// <summary>
        /// Get the currently tracked boss
        /// </summary>
        /// <returns>The current boss, or null if none</returns>
        public DiamondBoss GetCurrentBoss()
        {
            return currentBoss;
        }
        
        // Public methods for external control
        public void SetBossHealthBarPanel(GameObject newPanel)
        {
            bossHealthBarPanel = newPanel;
        }
        
        public void SetBossHealthBarFill(Image newFill)
        {
            bossHealthBarFill = newFill;
        }

        public void SetBossHealthBarBackground(Image newBackground)
        {
            bossHealthBarBackground = newBackground;
        }

        public void SetBossNameText(Text newText)
        {
            bossNameText = newText;
        }

        public void SetBossHealthText(Text newText)
        {
            bossHealthText = newText;
        }
    }
}
