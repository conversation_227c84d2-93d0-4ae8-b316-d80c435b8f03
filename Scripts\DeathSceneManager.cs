using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

namespace HELLSTRIKE
{
    /// <summary>
    /// Handles scene transitions when the player dies.
    /// Automatically loads the Scene_Lose when player health reaches 0.
    /// </summary>
    public class DeathSceneManager : MonoBehaviour
    {
        [Header("Scene Settings")]
        [SerializeField] private string loseSceneName = "Scene_Lose";
        [SerializeField] private float delayBeforeSceneChange = 2f;
        [SerializeField] private bool enableDebugLogs = true;
        
        [Header("Audio Settings")]
        [SerializeField] private AudioClip deathSound;
        [SerializeField] [Range(0f, 1f)] private float deathSoundVolume = 1f;
        
        [Header("Visual Effects")]
        [SerializeField] private bool enableFadeToBlack = true;
        [SerializeField] private float fadeToBlackDuration = 1f;
        [SerializeField] private Color fadeColor = Color.black;
        
        // Private variables
        private PlayerHealth playerHealth;
        private AudioSource audioSource;
        private bool isTransitioning = false;
        private CanvasGroup fadeCanvasGroup;
        
        private void Awake()
        {
            // Get or create audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            // Configure audio source
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 0f; // 2D sound
        }
        
        private void Start()
        {
            // Find player health component
            playerHealth = FindFirstObjectByType<PlayerHealth>();
            if (playerHealth == null)
            {
                Debug.LogError("DeathSceneManager: No PlayerHealth component found in scene!");
                return;
            }
            
            // Subscribe to player death event
            playerHealth.OnPlayerDeath.AddListener(OnPlayerDied);
            
            // Setup fade canvas if enabled
            if (enableFadeToBlack)
            {
                SetupFadeCanvas();
            }
            
            if (enableDebugLogs)
            {
                Debug.Log($"DeathSceneManager: Initialized. Will load '{loseSceneName}' on player death.");
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (playerHealth != null)
            {
                playerHealth.OnPlayerDeath.RemoveListener(OnPlayerDied);
            }
        }
        
        private void OnPlayerDied()
        {
            if (isTransitioning) return;
            
            isTransitioning = true;
            
            if (enableDebugLogs)
            {
                Debug.Log("DeathSceneManager: Player died! Starting scene transition...");
            }
            
            // Play death sound
            if (deathSound != null && audioSource != null)
            {
                audioSource.clip = deathSound;
                audioSource.volume = deathSoundVolume;
                audioSource.Play();
            }
            
            // Start transition coroutine
            StartCoroutine(TransitionToLoseScene());
        }
        
        private IEnumerator TransitionToLoseScene()
        {
            // Fade to black if enabled
            if (enableFadeToBlack && fadeCanvasGroup != null)
            {
                yield return StartCoroutine(FadeToBlack());
            }
            else
            {
                // Just wait for the delay
                yield return new WaitForSeconds(delayBeforeSceneChange);
            }
            
            // Load the lose scene
            if (enableDebugLogs)
            {
                Debug.Log($"DeathSceneManager: Loading scene '{loseSceneName}'");
            }
            
            try
            {
                SceneManager.LoadScene(loseSceneName);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"DeathSceneManager: Failed to load scene '{loseSceneName}': {e.Message}");
                Debug.LogError("Make sure the scene is added to Build Settings!");
            }
        }
        
        private void SetupFadeCanvas()
        {
            // Create a canvas for fade effect
            GameObject fadeCanvasGO = new GameObject("DeathFadeCanvas");
            fadeCanvasGO.transform.SetParent(transform);
            
            Canvas fadeCanvas = fadeCanvasGO.AddComponent<Canvas>();
            fadeCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
            fadeCanvas.sortingOrder = 1000; // Render on top of everything
            
            // Add CanvasGroup for fading
            fadeCanvasGroup = fadeCanvasGO.AddComponent<CanvasGroup>();
            fadeCanvasGroup.alpha = 0f;
            fadeCanvasGroup.interactable = false;
            fadeCanvasGroup.blocksRaycasts = false;
            
            // Create fade image
            GameObject fadeImageGO = new GameObject("FadeImage");
            fadeImageGO.transform.SetParent(fadeCanvasGO.transform, false);
            
            UnityEngine.UI.Image fadeImage = fadeImageGO.AddComponent<UnityEngine.UI.Image>();
            fadeImage.color = fadeColor;
            
            // Make it cover the entire screen
            RectTransform fadeRect = fadeImageGO.GetComponent<RectTransform>();
            fadeRect.anchorMin = Vector2.zero;
            fadeRect.anchorMax = Vector2.one;
            fadeRect.offsetMin = Vector2.zero;
            fadeRect.offsetMax = Vector2.zero;
        }
        
        private IEnumerator FadeToBlack()
        {
            if (fadeCanvasGroup == null) yield break;
            
            float elapsedTime = 0f;
            
            while (elapsedTime < fadeToBlackDuration)
            {
                elapsedTime += Time.deltaTime;
                float alpha = Mathf.Clamp01(elapsedTime / fadeToBlackDuration);
                fadeCanvasGroup.alpha = alpha;
                yield return null;
            }
            
            fadeCanvasGroup.alpha = 1f;
            
            // Wait for remaining delay time
            float remainingDelay = delayBeforeSceneChange - fadeToBlackDuration;
            if (remainingDelay > 0)
            {
                yield return new WaitForSeconds(remainingDelay);
            }
        }
        
        /// <summary>
        /// Manually trigger scene transition (for testing)
        /// </summary>
        [ContextMenu("Test Death Scene Transition")]
        public void TestDeathTransition()
        {
            if (!isTransitioning)
            {
                OnPlayerDied();
            }
        }
        
        /// <summary>
        /// Set a custom lose scene name
        /// </summary>
        /// <param name="sceneName">Name of the scene to load on death</param>
        public void SetLoseSceneName(string sceneName)
        {
            loseSceneName = sceneName;
            if (enableDebugLogs)
            {
                Debug.Log($"DeathSceneManager: Lose scene set to '{loseSceneName}'");
            }
        }
        
        /// <summary>
        /// Get the current lose scene name
        /// </summary>
        /// <returns>The scene name that will be loaded on death</returns>
        public string GetLoseSceneName()
        {
            return loseSceneName;
        }
    }
}
