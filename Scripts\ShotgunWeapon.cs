using UnityEngine;
using UnityEngine.UI;
using System.Collections;

namespace HELLSTRIKE
{
    public class ShotgunWeapon : MonoBehaviour
    {
        [Header("Gun Settings")]
        [SerializeField] private int maxAmmo = 8;
        [SerializeField] private int currentAmmo = 8;
        [SerializeField] private float damage = 15f; // Lower damage per pellet
        [SerializeField] private float range = 50f; // Shorter range than revolver
        [SerializeField] private float fireRate = 1.2f; // Slower fire rate
        [SerializeField] private float reloadTime = 2.5f; // Longer reload
        
        [Header("Shotgun Specific")]
        [SerializeField] private int pelletsPerShot = 8; // Number of pellets per shot
        [SerializeField] private float spreadAngle = 15f; // Spread angle in degrees
        
        [Header("Shooting")]
        [SerializeField] private Camera playerCamera;
        [SerializeField] private Transform gunBarrel; // Where bullets come from
        [SerializeField] private LayerMask shootableLayers = -1; // What can be shot (walls, enemies, etc.)
        [SerializeField] private bool autoFindCamera = true;
        [SerializeField] private bool debugShooting = true; // Show debug info
        
        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem muzzleFlash;
        [SerializeField] private GameObject impactEffect;
        [SerializeField] private float impactEffectLifetime = 2f;

        [Header("Bullet Trail")]
        [SerializeField] private LineRenderer bulletTrailPrefab; // Prefab for multiple trails
        [SerializeField] private bool enableBulletTrail = true;
        [SerializeField] private float trailDuration = 0.08f; // Faster fade for shotgun
        [SerializeField] private float trailWidth = 0.015f; // Thinner trails
        [SerializeField] private Color trailColor = Color.yellow; // Different color
        [SerializeField] private Material trailMaterial;
        
        [Header("Audio")]
        [SerializeField] private AudioClip shootSound;
        [SerializeField] private AudioClip reloadSound;
        [SerializeField] private AudioClip emptySound;
        [Range(0f, 1f)] [SerializeField] private float shootVolume = 0.8f; // Louder
        [Range(0f, 1f)] [SerializeField] private float reloadVolume = 0.6f;
        [Range(0f, 1f)] [SerializeField] private float emptyVolume = 0.5f;
        
        [Header("Recoil")]
        [SerializeField] private float recoilForce = 12f; // Stronger recoil
        [SerializeField] private float recoilUpwardForce = 10f; // More upward kick
        [SerializeField] private float recoilReturnSpeed = 12f;
        [SerializeField] private float recoilSnapDuration = 0.08f; // Longer snap
        [SerializeField] private float recoilReturnDuration = 0.25f; // Longer return
        [SerializeField] private Transform recoilTransform; // Gun model transform for recoil
        [SerializeField] private bool autoFindRecoilTransform = true; // Auto-find recoil transform
        [SerializeField] private bool debugRecoil = false; // Show recoil debug info
        
        [Header("UI")]
        [SerializeField] private Text ammoText; // Optional UI text for ammo count
        [SerializeField] private bool autoFindAmmoUI = true;
        
        // Private variables
        private AudioSource audioSource;
        private float lastFireTime;
        private bool isReloading = false;
        private Vector3 originalRecoilPosition;
        private Quaternion originalRecoilRotation;
        private bool canShoot = true;
        
        // Events
        public System.Action<int, int> OnAmmoChanged; // current, max
        public System.Action OnShoot;
        public System.Action OnReload;
        public System.Action OnEmpty;
        
        void Start()
        {
            // Auto-find camera if not assigned
            if (autoFindCamera && playerCamera == null)
            {
                playerCamera = Camera.main;
                if (playerCamera == null)
                {
                    playerCamera = FindFirstObjectByType<Camera>();
                }
            }
            
            // Auto-find ammo UI if enabled
            if (autoFindAmmoUI && ammoText == null)
            {
                GameObject ammoUI = GameObject.Find("AmmoText");
                if (ammoUI != null)
                {
                    ammoText = ammoUI.GetComponent<Text>();
                }
            }
            
            // Setup audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Auto-find recoil transform if enabled
            if (autoFindRecoilTransform && recoilTransform == null)
            {
                // Try to find a child object for recoil (like gun model)
                if (transform.childCount > 0)
                {
                    recoilTransform = transform.GetChild(0); // Use first child
                    Debug.Log($"ShotgunWeapon: Auto-found recoil transform: {recoilTransform.name}");
                }
                else
                {
                    // Use the weapon GameObject itself for recoil
                    recoilTransform = transform;
                    Debug.Log("ShotgunWeapon: Using weapon GameObject for recoil");
                }
            }

            // Store original recoil transform
            if (recoilTransform != null)
            {
                originalRecoilPosition = recoilTransform.localPosition;
                originalRecoilRotation = recoilTransform.localRotation;
                Debug.Log($"ShotgunWeapon: Recoil system initialized with {recoilTransform.name}");
            }
            else
            {
                Debug.LogWarning("ShotgunWeapon: No recoil transform found! Recoil will not work.");
            }

            // Setup bullet trail prefab
            SetupBulletTrail();

            // Initialize ammo display
            UpdateAmmoUI();
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
        }
        
        void Update()
        {
            HandleInput();
            HandleRecoil();
        }
        
        private void HandleInput()
        {
            // Shooting input (left mouse button)
            if (Input.GetButtonDown("Fire1") && canShoot)
            {
                Shoot();
            }
            
            // Reload input (R key)
            if (Input.GetKeyDown(KeyCode.R) && !isReloading && currentAmmo < maxAmmo)
            {
                StartCoroutine(Reload());
            }
        }
        
        private void Shoot()
        {
            // Check if we can shoot
            if (isReloading || Time.time - lastFireTime < fireRate)
                return;
            
            // Check ammo
            if (currentAmmo <= 0)
            {
                PlayEmptySound();
                OnEmpty?.Invoke();
                return;
            }
            
            // Fire multiple pellets
            for (int i = 0; i < pelletsPerShot; i++)
            {
                PerformHitscan();
            }
            
            // Consume ammo
            currentAmmo--;
            lastFireTime = Time.time;
            
            // Visual and audio effects
            PlayShootEffects();
            
            // Trigger recoil
            if (recoilTransform != null)
            {
                StartCoroutine(ApplyRecoil());
            }
            
            // Update UI and trigger events
            UpdateAmmoUI();
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
            OnShoot?.Invoke();
            
            Debug.Log($"ShotgunWeapon: Shot fired with {pelletsPerShot} pellets! Ammo remaining: {currentAmmo}/{maxAmmo}");
        }
        
        private Vector3 PerformHitscan()
        {
            if (playerCamera == null)
            {
                Debug.LogWarning("ShotgunWeapon: No camera assigned for hitscan!");
                return Vector3.zero;
            }

            // Create ray from camera center with random spread
            Vector3 screenCenter = new Vector3(Screen.width / 2, Screen.height / 2, 0);
            
            // Add random spread
            float randomX = Random.Range(-spreadAngle, spreadAngle);
            float randomY = Random.Range(-spreadAngle, spreadAngle);
            Vector3 spreadOffset = new Vector3(randomX, randomY, 0);
            
            Ray ray = playerCamera.ScreenPointToRay(screenCenter + spreadOffset);
            RaycastHit[] hits;

            Vector3 targetPoint = ray.origin + ray.direction * range; // Default to max range
            bool enemyHit = false;

            if (debugShooting)
            {
                Debug.Log($"ShotgunWeapon: Firing pellet from {ray.origin} in direction {ray.direction}");
            }

            // Get all hits along the ray, sorted by distance
            hits = Physics.RaycastAll(ray, range, shootableLayers);
            System.Array.Sort(hits, (x, y) => x.distance.CompareTo(y.distance));

            // Process hits in order of distance
            foreach (RaycastHit hit in hits)
            {
                GameObject hitObject = hit.collider.gameObject;

                if (debugShooting)
                {
                    Debug.Log($"ShotgunWeapon: Pellet hit {hitObject.name} at distance {hit.distance:F2}");
                }

                // Try different enemy script types
                var chaserEnemy = hitObject.GetComponent<ChaserEnemy>();
                var patrolEnemy = hitObject.GetComponent<Enemy_Patrol>();
                var spiderEnemy = hitObject.GetComponent<ExplodingSpider>();
                var turretEnemy = hitObject.GetComponent<TurretEnemy>();
                var diamondBoss = hitObject.GetComponent<HELLSTRIKE.DiamondBoss>();

                if (chaserEnemy != null)
                {
                    chaserEnemy.TakeDamage(damage);
                    enemyHit = true;
                    targetPoint = hit.point;
                    Debug.Log($"ShotgunWeapon: Pellet hit ChaserEnemy for {damage} damage!");
                    break; // Stop at first enemy hit
                }
                else if (patrolEnemy != null)
                {
                    patrolEnemy.TakeDamage(damage);
                    enemyHit = true;
                    targetPoint = hit.point;
                    Debug.Log($"ShotgunWeapon: Pellet hit Enemy_Patrol for {damage} damage!");
                    break; // Stop at first enemy hit
                }
                else if (spiderEnemy != null)
                {
                    spiderEnemy.TakeDamage(damage);
                    enemyHit = true;
                    targetPoint = hit.point;
                    Debug.Log($"ShotgunWeapon: Pellet hit ExplodingSpider for {damage} damage!");
                    break; // Stop at first enemy hit
                }
                else if (turretEnemy != null)
                {
                    turretEnemy.TakeDamage(damage);
                    enemyHit = true;
                    targetPoint = hit.point;
                    Debug.Log($"ShotgunWeapon: Pellet hit TurretEnemy for {damage} damage!");
                    break; // Stop at first enemy hit
                }
                else if (diamondBoss != null)
                {
                    diamondBoss.TakeDamage(damage);
                    enemyHit = true;
                    targetPoint = hit.point;
                    Debug.Log($"ShotgunWeapon: Pellet hit DiamondBoss for {damage} damage!");
                    break; // Stop at first enemy hit
                }
                else
                {
                    // Hit something else (wall, obstacle, etc.) - continue checking for enemies behind it
                    if (debugShooting)
                    {
                        Debug.Log($"ShotgunWeapon: Pellet hit non-enemy object {hitObject.name}, continuing search");
                    }

                    // If this is a solid object (not a trigger), stop here
                    if (!hit.collider.isTrigger)
                    {
                        targetPoint = hit.point;

                        // Spawn impact effect on walls/obstacles
                        if (impactEffect != null)
                        {
                            GameObject impact = Instantiate(impactEffect, hit.point, Quaternion.LookRotation(hit.normal));
                            Destroy(impact, impactEffectLifetime);
                        }
                        break;
                    }
                }
            }

            // If no hits at all
            if (hits.Length == 0)
            {
                targetPoint = ray.origin + ray.direction * range;
                if (debugShooting)
                {
                    Debug.Log("ShotgunWeapon: Pellet missed - no objects hit");
                }
            }

            // If we hit an enemy, spawn impact effect
            if (enemyHit && impactEffect != null && hits.Length > 0)
            {
                RaycastHit enemyHit_info = System.Array.Find(hits, h =>
                    h.collider.GetComponent<ChaserEnemy>() != null ||
                    h.collider.GetComponent<Enemy_Patrol>() != null ||
                    h.collider.GetComponent<ExplodingSpider>() != null ||
                    h.collider.GetComponent<TurretEnemy>() != null ||
                    h.collider.GetComponent<HELLSTRIKE.DiamondBoss>() != null);

                if (enemyHit_info.collider != null)
                {
                    GameObject impact = Instantiate(impactEffect, enemyHit_info.point, Quaternion.LookRotation(enemyHit_info.normal));
                    Destroy(impact, impactEffectLifetime);
                }
            }

            // Create bullet trail
            Vector3 startPoint = gunBarrel != null ? gunBarrel.position : ray.origin;
            CreateBulletTrail(startPoint, targetPoint);

            // Draw debug line in scene view
            if (hits.Length > 0)
            {
                Debug.DrawLine(ray.origin, hits[0].point, enemyHit ? Color.red : Color.yellow, 0.5f);
            }
            else
            {
                Debug.DrawLine(ray.origin, ray.origin + ray.direction * range, Color.white, 0.5f);
            }

            return targetPoint;
        }

        private void PlayShootEffects()
        {
            // Play muzzle flash
            if (muzzleFlash != null)
            {
                muzzleFlash.Play();
            }

            // Play shoot sound
            if (shootSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(shootSound, shootVolume);
            }
        }

        private void PlayEmptySound()
        {
            if (emptySound != null && audioSource != null)
            {
                audioSource.PlayOneShot(emptySound, emptyVolume);
            }
        }

        private IEnumerator Reload()
        {
            if (isReloading || currentAmmo >= maxAmmo)
                yield break;

            isReloading = true;
            Debug.Log("ShotgunWeapon: Reloading...");

            // Play reload sound
            if (reloadSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(reloadSound, reloadVolume);
            }

            OnReload?.Invoke();

            // Wait for reload time
            yield return new WaitForSeconds(reloadTime);

            // Refill ammo
            currentAmmo = maxAmmo;
            isReloading = false;

            // Update UI
            UpdateAmmoUI();
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);

            Debug.Log("ShotgunWeapon: Reload complete!");
        }

        private IEnumerator ApplyRecoil()
        {
            if (recoilTransform == null)
            {
                if (debugRecoil)
                    Debug.LogWarning("ShotgunWeapon: Cannot apply recoil - no recoil transform assigned!");
                yield break;
            }

            // More aggressive recoil with upward rotation and backward movement
            Vector3 recoilOffset = new Vector3(0, recoilUpwardForce * 0.03f, -recoilForce * 0.06f);
            Vector3 recoilRotation = new Vector3(-recoilUpwardForce * 2.5f, Random.Range(-2f, 2f), 0); // Negative X = muzzle up rotation

            if (debugRecoil)
            {
                Debug.Log($"ShotgunWeapon: Applying recoil - Offset: {recoilOffset}, Rotation: {recoilRotation}");
            }

            float elapsed = 0f;

            // Snap to recoil position (very fast and aggressive)
            while (elapsed < recoilSnapDuration)
            {
                float progress = elapsed / recoilSnapDuration;
                // Use a sharp curve for snappy recoil
                float curve = Mathf.Pow(progress, 0.3f);

                recoilTransform.localPosition = Vector3.Lerp(originalRecoilPosition, originalRecoilPosition + recoilOffset, curve);
                recoilTransform.localRotation = Quaternion.Lerp(originalRecoilRotation, originalRecoilRotation * Quaternion.Euler(recoilRotation), curve);

                elapsed += Time.deltaTime;
                yield return null;
            }

            // Ensure we hit the peak recoil
            recoilTransform.localPosition = originalRecoilPosition + recoilOffset;
            recoilTransform.localRotation = originalRecoilRotation * Quaternion.Euler(recoilRotation);

            // Return to original position (smooth but still fast)
            elapsed = 0f;

            while (elapsed < recoilReturnDuration)
            {
                float progress = elapsed / recoilReturnDuration;
                // Use easing for smooth return
                float curve = 1f - Mathf.Pow(1f - progress, 3f);

                recoilTransform.localPosition = Vector3.Lerp(originalRecoilPosition + recoilOffset, originalRecoilPosition, curve);
                recoilTransform.localRotation = Quaternion.Lerp(originalRecoilRotation * Quaternion.Euler(recoilRotation), originalRecoilRotation, curve);

                elapsed += Time.deltaTime;
                yield return null;
            }

            // Ensure exact original position
            recoilTransform.localPosition = originalRecoilPosition;
            recoilTransform.localRotation = originalRecoilRotation;
        }

        private void HandleRecoil()
        {
            // This method can be used for additional recoil handling if needed
        }

        private void UpdateAmmoUI()
        {
            if (ammoText != null)
            {
                ammoText.text = $"{currentAmmo}/{maxAmmo}";
            }
        }

        private void SetupBulletTrail()
        {
            // For shotgun, we'll create trails dynamically since we need multiple
            if (bulletTrailPrefab == null && enableBulletTrail)
            {
                GameObject trailObject = new GameObject("BulletTrailPrefab");
                trailObject.transform.SetParent(transform);
                bulletTrailPrefab = trailObject.AddComponent<LineRenderer>();

                // Configure the prefab
                if (bulletTrailPrefab != null)
                {
                    bulletTrailPrefab.material = trailMaterial;
                    bulletTrailPrefab.startWidth = trailWidth;
                    bulletTrailPrefab.endWidth = trailWidth;
                    bulletTrailPrefab.positionCount = 2;
                    bulletTrailPrefab.useWorldSpace = true;
                    bulletTrailPrefab.enabled = false;

                    // If no material assigned, create a simple unlit material
                    if (bulletTrailPrefab.material == null)
                    {
                        bulletTrailPrefab.material = new Material(Shader.Find("Sprites/Default"));
                        bulletTrailPrefab.material.color = trailColor;
                    }

                    // Set color using gradient
                    Gradient gradient = new Gradient();
                    gradient.SetKeys(
                        new GradientColorKey[] { new GradientColorKey(trailColor, 0.0f), new GradientColorKey(trailColor, 1.0f) },
                        new GradientAlphaKey[] { new GradientAlphaKey(1.0f, 0.0f), new GradientAlphaKey(1.0f, 1.0f) }
                    );
                    bulletTrailPrefab.colorGradient = gradient;
                }
            }
        }

        private void CreateBulletTrail(Vector3 startPoint, Vector3 endPoint)
        {
            if (!enableBulletTrail || bulletTrailPrefab == null)
                return;

            // Create a new trail instance for this pellet
            GameObject trailInstance = new GameObject("ShotgunTrail");
            LineRenderer trail = trailInstance.AddComponent<LineRenderer>();

            // Copy settings from prefab
            trail.material = bulletTrailPrefab.material;
            trail.startWidth = bulletTrailPrefab.startWidth;
            trail.endWidth = bulletTrailPrefab.endWidth;
            trail.positionCount = 2;
            trail.useWorldSpace = true;
            trail.colorGradient = bulletTrailPrefab.colorGradient;

            // Set trail positions
            trail.SetPosition(0, startPoint);
            trail.SetPosition(1, endPoint);

            // Start coroutine to fade out the trail
            StartCoroutine(FadeBulletTrail(trail, trailInstance));
        }

        private System.Collections.IEnumerator FadeBulletTrail(LineRenderer trail, GameObject trailObject)
        {
            if (trail == null) yield break;

            float elapsed = 0f;
            Color originalColor = trailColor;

            while (elapsed < trailDuration)
            {
                float alpha = Mathf.Lerp(1f, 0f, elapsed / trailDuration);
                Color fadeColor = new Color(originalColor.r, originalColor.g, originalColor.b, alpha);

                // Update gradient with fading alpha
                Gradient gradient = new Gradient();
                gradient.SetKeys(
                    new GradientColorKey[] { new GradientColorKey(fadeColor, 0.0f), new GradientColorKey(fadeColor, 1.0f) },
                    new GradientAlphaKey[] { new GradientAlphaKey(alpha, 0.0f), new GradientAlphaKey(alpha, 1.0f) }
                );
                trail.colorGradient = gradient;

                elapsed += Time.deltaTime;
                yield return null;
            }

            // Destroy the trail object
            if (trailObject != null)
            {
                Destroy(trailObject);
            }
        }

        // Public methods for external access
        public void SetAmmo(int newAmmo)
        {
            currentAmmo = Mathf.Clamp(newAmmo, 0, maxAmmo);
            UpdateAmmoUI();
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
        }

        public void AddAmmo(int amount)
        {
            SetAmmo(currentAmmo + amount);
        }

        public bool HasAmmo()
        {
            return currentAmmo > 0;
        }

        public bool IsReloading()
        {
            return isReloading;
        }

        public void SetCanShoot(bool canShoot)
        {
            this.canShoot = canShoot;
        }

        public void SetDebugShooting(bool debug)
        {
            debugShooting = debug;
        }

        // Public method to set recoil transform (for WeaponManager)
        public void SetRecoilTransform(Transform recoilTransform)
        {
            this.recoilTransform = recoilTransform;
            if (recoilTransform != null)
            {
                originalRecoilPosition = recoilTransform.localPosition;
                originalRecoilRotation = recoilTransform.localRotation;
                Debug.Log($"ShotgunWeapon: Recoil transform set to {recoilTransform.name}");
            }
        }

        public Transform GetRecoilTransform()
        {
            return recoilTransform;
        }

        // Properties
        public int CurrentAmmo => currentAmmo;
        public int MaxAmmo => maxAmmo;
        public float Damage => damage;
        public float Range => range;
        public int PelletsPerShot => pelletsPerShot;
    }
}
